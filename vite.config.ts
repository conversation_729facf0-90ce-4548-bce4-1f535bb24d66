import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 设置 process.env.NODE_ENV
  // 注意：Vite不允许在.env文件中设置 NODE_ENV=production
  if (mode === 'production') {
    process.env.NODE_ENV = 'production';
  } else {
    process.env.NODE_ENV = 'development';
  }
  
  // 使用Vite的默认环境变量加载机制
  // 我们的环境文件结构:
  // .env - 所有环境共用的基础变量（默认加载）
  // .env.localhost - 本地开发环境变量 (--mode localhost)
  // .env.dev - 开发环境变量 (--mode dev)
  // .env.prod - 生产环境变量 (--mode prod)
  
  // 使用标准的环境变量加载
  const env = loadEnv(mode, process.cwd(), '');
  
  // 打印当前环境变量信息
  if (mode !== 'production') {
    console.log('\n=========== Current Environment Variables ===========');
    console.log(`Mode: ${mode}`);
    
    // 打印安全的环境变量(排除敏感信息)
    const safeEnv: Record<string, string> = {...env};
    if ('VITE_APP_OPENAI_API_KEY' in safeEnv) {
      safeEnv['VITE_APP_OPENAI_API_KEY'] = '******';
    }
    
    Object.keys(safeEnv).forEach(key => {
      if (key.startsWith('VITE_')) {
        console.log(`${key}: ${safeEnv[key]}`);
      }
    });
    console.log('=================================================\n');
  }

  // 创建一个定义对象，明确将环境变量传递给前端
  const envDefine: Record<string, string> = {};
  
  // 遍历环境变量，将所有VITE_开头的变量设置到define对象中
  Object.keys(env).forEach(key => {
    if (key.startsWith('VITE_')) {
      // 添加import.meta.env前缀
      envDefine[`import.meta.env.${key}`] = JSON.stringify(env[key]);
    }
  });

  return {
    plugins: [react()],
    define: envDefine, // 将环境变量显式定义到前端代码中
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 80, // 设置本地开发服务器端口为80
      allowedHosts: ['fans-omniview.local.biz.weibo.com'], // 允许指定域名访问
    }
  };
})
