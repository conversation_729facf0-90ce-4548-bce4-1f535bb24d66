{"name": "fans-omnioview", "private": true, "version": "0.0.0", "type": "module", "scripts": {"local": "vite --mode localhost", "dev": "vite --mode dev", "build": "tsc -b && vite build --mode prod", "build:dev": "tsc -b && vite build --mode dev", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/x": "^1.2.0", "@types/node": "^22.15.3", "antd": "^5.24.8", "axios": "^1.9.0", "openai": "^4.96.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}