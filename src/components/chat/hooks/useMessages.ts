import { useRef, useEffect, useMemo, useState } from 'react';
import { Message } from '../types';

/**
 * 自定义Hook: 管理聊天消息和滚动逻辑
 * 
 * @param xMessages XChat消息
 * @returns { formattedMessages, messagesContainerRef } 格式化的消息和容器引用
 */
export const useMessages = (xMessages: any[]) => {
  // 创建对消息容器的引用，用于滚动操作
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  
  // 利用 useMemo 高效转换消息格式
  const formattedMessages = useMemo<Message[]>(() => {
    // 过滤有效消息并转换为 MessageList 所需格式
    return xMessages
      .filter(xMsg => xMsg && xMsg.message) // 过滤无效消息
      .map(xMsg => ({
        id: xMsg.id.toString(),
        message: xMsg.message,
        type: xMsg.status === 'local' ? 'user' : 'assistant'
      }));
  }, [xMessages]);
  
  // 跟踪用户是否手动滚动
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  // 存储上一次消息数量，用于检测新消息
  const prevMessagesCountRef = useRef<number>(0);
  
  // 检测用户滚动
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;
    
    const handleScroll = () => {
      // 检测用户是否向上滚动
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 10; // 允许小误差
      
      // 只有用户不在底部时才标记为已滚动
      if (!isAtBottom) {
        setUserHasScrolled(true);
      } else {
        // 当用户回到底部时，重置标记
        setUserHasScrolled(false);
      }
    };
    
    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);
  
  // 跟踪上一次消息的ID，用于检测新消息
  const prevLastMessageIdRef = useRef<string>('');

  
  // 处理消息变化与自动滚动
  useEffect(() => {
    if (formattedMessages.length === 0) return;
    
    // 检测是否有新消息 (通过消息数量和最后一条消息ID双重检查)
    const lastMessageId = formattedMessages[formattedMessages.length - 1]?.id || '';
    const hasNewMessages = formattedMessages.length > prevMessagesCountRef.current || 
                          (lastMessageId && lastMessageId !== prevLastMessageIdRef.current);
    
    // 更新引用值
    prevMessagesCountRef.current = formattedMessages.length;
    prevLastMessageIdRef.current = lastMessageId;
    
    // 如果没有手动滚动或者有新消息，则滚动到底部
    if (!userHasScrolled || hasNewMessages) {
      // 使用紧况定时器确保滚动发生
      const scrollTimeout = setTimeout(() => {
        const container = messagesContainerRef.current;
        if (container) {
          // 直接滚动容器到底部，而不是滚动最后一个元素
          // 这种方式更可靠
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
          
          // 如果是新消息触发的滚动，依然保持userHasScrolled状态
          // 这样用户可以继续查看上方消息，直到他们自己再次滚动到底部
        }
      }, 100); // 延时确保消息已渲染
      
      return () => clearTimeout(scrollTimeout);
    }
  }, [formattedMessages, userHasScrolled]);
  
  return {
    formattedMessages,
    messagesContainerRef
  };
};

export default useMessages;
