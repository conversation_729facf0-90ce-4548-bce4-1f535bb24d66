import { Avatar, Button, Dropdown, Empty, Input, List, Modal, Spin, Typography, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { getUserName, logout } from '../../../services/authService';
// 导入样式表
import {
  CopyOutlined,
  DeleteOutlined,
  LogoutOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined,
  UserOutlined
} from '@ant-design/icons';
import './ChatSidebar.css';

// 导入本地默认头像图片（适用于未设置头像时）
import defaultAvatar from '../../../assets/avatar.png';

import { ConversationItem } from '../types';

const { Text } = Typography;

interface ChatSidebarProps {
  conversationItems: ConversationItem[];
  activeChatId: string;
  onConversationClick: (key: string) => void;
  onAddConversation: () => void;
  onDeleteConversation?: (key: string) => void;
  loading?: boolean;
  // 用户信息
  userInfo?: {
    avatar?: string;
    nickname?: string;
  };
}

// 工具函数：根据分组键返回"今天/昨天/更早之前"
const getDateGroupLabel = (groupKey: string) => {
  if (groupKey === 'today') return '今天';
  if (groupKey === 'yesterday') return '昨天';
  if (groupKey === 'earlier') return '更早之前';
  return groupKey; // 默认返回原始键
};

const ChatSidebar: React.FC<ChatSidebarProps> = ({
  conversationItems,
  activeChatId,
  onConversationClick,
  onAddConversation,
  onDeleteConversation,
  loading = false,
  userInfo = { avatar: '', nickname: '用户' },
}) => {
  // 使用状态来存储用户信息
  const [localUserInfo, setLocalUserInfo] = useState(userInfo);

  // 使用 Modal hook API
  const [modal, contextHolder] = Modal.useModal();

  // 重新登录下拉菜单项

  // 重新登录处理
  const handleRelogin = () => {
    // 直接调用登出方法，跳转CAS登录页
    logout();
  };

  // 挂载时从 authService 获取用户名
  useEffect(() => {
    const name = getUserName();
    if (name) {
      setLocalUserInfo(prev => ({ ...prev, nickname: name }));
    }
  }, []);
  // 不再需要 token 变量，因为我们已将所有样式移动到 CSS 文件中
  const [searchText, setSearchText] = useState('');

  // 过滤会话列表
  const filteredItems = conversationItems.filter(item =>
    item.label.toLowerCase().includes(searchText.toLowerCase())
  );

  // 确认删除会话
  const confirmDelete = (key: string) => {
    if (onDeleteConversation) {
      onDeleteConversation(key);
      // 删除成功消息由 handleDeleteConversation 处理，这里不需要重复显示
    }
  };

  // 复制会话ID
  const handleCopyConversationId = async (chatId: string) => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        // 使用现代 Clipboard API
        await navigator.clipboard.writeText(chatId);
      } else {
        // 降级方案：使用传统的 document.execCommand
        const textArea = document.createElement('textarea');
        textArea.value = chatId;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
      message.success('会话ID已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败，请手动复制');
    }
  };

  // 按分组对会话进行分类
  const groupedConversations = useMemo(() => {
    const groups: Record<string, ConversationItem[]> = {
      'today': [],
      'yesterday': [],
      'earlier': []
    };

    // 对会话进行分组
    filteredItems.forEach(item => {
      if (groups[item.group]) {
        groups[item.group].push(item);
      } else {
        groups['earlier'].push(item);
      }
    });

    // 对每个分组内的会话进行排序（按时间戳降序）
    Object.keys(groups).forEach(group => {
      groups[group].sort((a, b) => {
        if (a.timestamp && b.timestamp) {
          return b.timestamp - a.timestamp;
        }
        return 0;
      });
    });

    // 按照今天、昨天、更早之前的顺序排列
    const result: [string, ConversationItem[]][] = [];

    // 只添加非空的分组
    if (groups['today'].length > 0) {
      result.push(['today', groups['today']]);
    }

    if (groups['yesterday'].length > 0) {
      result.push(['yesterday', groups['yesterday']]);
    }

    if (groups['earlier'].length > 0) {
      result.push(['earlier', groups['earlier']]);
    }

    return result;
  }, [filteredItems]);

  // 使用CSS类名替代内联样式

  return (
    <>
      {contextHolder}
      <div className="chat-sidebar">
      {/* Logo */}
      <div className="chat-sidebar__header">
        <img
          src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*eco6RrQhxbMAAAAAAAAAAAAADgCCAQ/original"
          draggable={false}
          alt="logo"
          className="chat-sidebar__logo-img"
        />
        <span className="chat-sidebar__logo-text">粉条Labs</span>
      </div>

      {/* 搜索框和添加会话按钮 */}
      <div className="chat-sidebar__search">
        <Input
          placeholder="搜索会话"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          className="chat-sidebar__search-input"
          allowClear
        />
        <Button
          onClick={onAddConversation}
          type="primary"
          icon={<PlusOutlined />}
          className="chat-sidebar__add-button"
        />
      </div>

      {/* 会话列表（按日期分组） */}
      <div className="chat-sidebar__conversations">
        {loading ? (
          <div className="chat-sidebar__placeholder">
            <Spin spinning={true} size="default" />
          </div>
        ) : conversationItems.length === 0 ? (
          <div className="chat-sidebar__placeholder">
            <Empty description="暂无会话" />
            <Button
              type="primary"
              className="chat-sidebar__create-button"
              style={{ marginTop: 16 }}
              onClick={onAddConversation}
            >
              创建新会话
            </Button>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="chat-sidebar__placeholder">
            <Empty description="未找到匹配的会话" />
          </div>
        ) : (
          <>
            {groupedConversations.map(([group, items]) => (
              <div key={group} className="chat-sidebar__group">
                <div className="chat-sidebar__date-label">
                  {getDateGroupLabel(group)}
                </div>
                <List
                  dataSource={items}
                  size="small"
                  split={false}
                  className="chat-sidebar__list"
                  renderItem={item => (
                    <List.Item
                      key={item.key}
                      className={`chat-sidebar__item ${item.key === activeChatId ? 'chat-sidebar__item--active' : ''}`}
                      onClick={() => onConversationClick(item.key)}
                      actions={[
                        <Dropdown
                          key="more"
                          menu={{
                            items: [
                              {
                                key: 'copy',
                                icon: <CopyOutlined />,
                                label: '复制会话ID',
                                onClick: (e) => {
                                  e?.domEvent?.stopPropagation();
                                  handleCopyConversationId(item.key);
                                }
                              },
                              {
                                type: 'divider'
                              },
                              {
                                key: 'delete',
                                icon: <DeleteOutlined />,
                                label: '删除会话',
                                danger: true,
                                onClick: (e) => {
                                  e?.domEvent?.stopPropagation();

                                  // 使用 Modal hook API 创建确认对话框
                                  modal.confirm({
                                    title: '删除会话',
                                    content: '确定要删除这个会话吗？此操作不可恢复。',
                                    okText: '确认',
                                    cancelText: '取消',
                                    okType: 'danger',
                                    onOk: () => {
                                      confirmDelete(item.key);
                                    }
                                  });
                                }
                              }
                            ]
                          }}
                          placement="bottomRight"
                          trigger={['click']}
                          arrow={{ pointAtCenter: true }}
                        >
                          <Button
                            type="text"
                            className="chat-sidebar__more-button"
                            icon={<MoreOutlined />}
                            size="small"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Dropdown>
                      ]}
                    >
                      <div className="chat-sidebar__item-content">
                        <Typography.Text
                          ellipsis={{ tooltip: item.label }}
                          className={item.key === activeChatId ? 'chat-sidebar__item-text--active' : 'chat-sidebar__item-text'}
                        >
                          {item.label}
                        </Typography.Text>
                      </div>
                    </List.Item>
                  )}
                />
              </div>
            ))}
          </>
        )}
      </div>

      {/* 用户信息区域 - 吸底定位 */}
      <div className="chat-sidebar__user-info">
        <Avatar
          size={40}
          icon={<UserOutlined />}
          src={localUserInfo.avatar && localUserInfo.avatar.length > 0 ? localUserInfo.avatar : defaultAvatar}
          className="chat-sidebar__avatar"
        />
        <div className="chat-sidebar__user-name">
          <Text strong className="chat-sidebar__username">{localUserInfo.nickname}</Text>
        </div>

        <Dropdown
          menu={{
            items: [
              {
                key: 'relogin',
                icon: <LogoutOutlined />,
                label: '重新登录',
                onClick: handleRelogin
              }
            ]
          }}
          placement="topRight"
          trigger={['click']}
          overlayStyle={{ minWidth: '120px' }}
          arrow={{ pointAtCenter: true }}
        >
          <Button
            type="text"
            icon={<SettingOutlined />}
            className="chat-sidebar__settings"
          />
        </Dropdown>
      </div>
    </div>
    </>
  );
};

export default ChatSidebar;
