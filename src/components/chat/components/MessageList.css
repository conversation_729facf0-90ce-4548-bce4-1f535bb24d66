/* Markdown内容样式 */
.markdown-content {
  /* 基础样式 */
  font-size: 14px;
  line-height: 1.6;
}

/* 段落样式 */
.markdown-content p {
  margin: 0.5em 0;
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

/* 行内代码样式 */
.markdown-content code {
  background: rgba(0, 0, 0, 0.06);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

/* 代码块样式 */
.markdown-content pre {
  /*background: rgba(0, 0, 0, 0.06);*/
  background: transparent;
  padding: 0.5em;
  border-radius: 5px;
  overflow-x: auto;
  margin: 0.8em 0;
}

.markdown-content pre code {
  background: transparent;
  padding: 0;
  border-radius: 0;
  display: block;
  line-height: 1.5;
}

/* 引用样式 */
.markdown-content blockquote {
  border-left: 4px solid #ddd;
  padding: 0 1em;
  color: #666;
  margin: 0.5em 0;
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 500;
}

/* 表格样式 */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.8em 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #eee;
  padding: 0.5em;
  text-align: left;
}

.markdown-content th {
  background-color: #f5f5f5;
}

/* 链接样式 */
.markdown-content a {
  color: #1890ff;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* 图片样式 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0.8em 0;
}
