import { Bubble } from '@ant-design/x';
import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'; // 引入Prism高亮组件
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'; // 选择高亮主题，可根据需要更换
import { Message } from '../types';
import { WelcomeScreen } from './index';
// 导入Markdown样式
import './MessageList.css';

/**
 * 消息列表组件
 * 负责渲染聊天消息或欢迎屏幕
 * 支持Markdown格式化显示
 * 支持连续的非用户消息合并展示
 */
const MessageList: React.FC<{ messages: Message[] }> = React.memo(({ messages }) => {
  if (messages.length === 0) {
    return <WelcomeScreen />;
  }
  
  /**
   * 渲染Markdown内容，支持代码块语法高亮
   * @param content markdown文本
   */
  const renderMarkdown = (content: string) => {
    return (
      <ReactMarkdown
        // 自定义代码块渲染，支持语法高亮
        components={{
          code({ node, inline, className, children, ...props }: { node?: any; inline?: boolean; className?: string; children?: React.ReactNode; [key: string]: any }) {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <SyntaxHighlighter
                style={oneLight} // 主题风格
                language={match[1]}
                showLineNumbers={true}
                PreTag="div"
                customStyle={{
                  maxHeight: '300px',
                  overflowY: 'auto',
                  borderRadius: '8px'
                }}
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          }
        }}
      >
        {content}
      </ReactMarkdown>
    );
  };

  /**
   * 合并连续的非用户消息
   * @param messages 原始消息数组
   * @returns 处理后的消息数组，连续的assistant消息被合并
   */
  const processMergedMessages = (originalMessages: Message[]) => {
    const result: Array<{
      key: string;
      role: 'local' | 'ai';
      content: React.ReactNode | string;
    }> = [];

    let currentAssistantMessages: Message[] = [];

    // 遍历所有消息，将连续的assistant消息合并
    originalMessages.forEach((msg, index) => {
      if (msg.type === 'assistant') {
        // 收集连续的assistant消息
        currentAssistantMessages.push(msg);
        
        // 如果是最后一条消息或下一条是用户消息，则合并当前收集的assistant消息
        if (index === originalMessages.length - 1 || originalMessages[index + 1].type === 'user') {
          if (currentAssistantMessages.length > 0) {
            // 合并多条assistant消息
            const mergedContent = currentAssistantMessages.map(assistantMsg => {
              // 为每条消息添加一个隐形的分隔符，确保不同消息之间有合适的间隔
              return renderMarkdown(assistantMsg.message);
            });
            
            result.push({
              key: currentAssistantMessages.map(m => m.id).join('-'),
              role: 'ai',
              content: <div className="merged-assistant-messages">{mergedContent}</div>
            });
            
            // 重置收集器
            currentAssistantMessages = [];
          }
        }
      } else {
        // 处理用户消息前，确保之前收集的assistant消息已处理
        if (currentAssistantMessages.length > 0) {
          const mergedContent = currentAssistantMessages.map(assistantMsg => {
            return renderMarkdown(assistantMsg.message);
          });
          
          result.push({
            key: currentAssistantMessages.map(m => m.id).join('-'),
            role: 'ai',
            content: <div className="merged-assistant-messages">{mergedContent}</div>
          });
          
          // 重置收集器
          currentAssistantMessages = [];
        }
        
        // 添加用户消息
        result.push({
          key: msg.id,
          role: 'local',
          content: msg.message
        });
      }
    });

    return result;
  };
  
  // 处理并合并消息
  const processedItems = processMergedMessages(messages);
  
  return (
    <Bubble.List
      items={processedItems}
      roles={{
        ai: {
          placement: 'start',
          typing: { step: 5, interval: 20 },
          styles: { 
            content: { 
              borderRadius: '16px',
              backgroundColor: 'transparent', // AI 气泡背景设置为透明
            } 
          },
          // 添加所需的自定义样式
          className: 'markdown-content',
        },
        local: {
          placement: 'end',
          variant: 'shadow',
          styles: {
            content: {
              backgroundColor: '#E9E9EB', // 用户气泡的新背景色 
            }
          },
        },
      }}
    />
  );
});

export default MessageList;
