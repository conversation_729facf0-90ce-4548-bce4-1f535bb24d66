/* ChatSidebar 组件样式 */

/* 主容器样式 */
.chat-sidebar {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  background-color: #f9f9f9; /* 更浅的灰色背景 */
  border-right: 1px solid rgba(0, 0, 0, 0.08); /* 增强边框对比度 */
  overflow: hidden;
  box-shadow: 1px 0 4px rgba(0, 0, 0, 0.03); /* 添加轻微阴影增强层次感 */
  width: 280px; /* 明确指定宽度 */
  box-sizing: border-box; /* 确保边框不会增加宽度 */
}

/* 头部区域样式 */
.chat-sidebar__header {
  flex-shrink: 0;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #f9f9f9; /* 与侧边栏保持一致 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02); /* 轻微阴影 */
  display: flex;
  align-items: center; /* 确保内部元素垂直居中对齐 */
}

/* Logo 图片样式 */
.chat-sidebar__logo-img {
  height: 28px;
  margin-right: 8px;
  display: inline-block;
  vertical-align: middle; /* 垂直居中对齐 */
}

/* Logo 文字样式 */
.chat-sidebar__logo-text {
  font-size: 16px;
  font-weight: bold;
  display: inline-block;
  vertical-align: middle; /* 垂直居中对齐 */
  line-height: 28px; /* 与logo高度一致 */
}

/* 搜索区域样式 */
.chat-sidebar__search {
  display: flex;
  padding: 12px 16px;
  align-items: center;
  flex-shrink: 0;
  background-color: #f9f9f9;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  margin-bottom: 8px;
}

/* 搜索输入框样式 */
.chat-sidebar__search-input {
  flex: 1;
  margin-right: 8px;
}

/* 添加按钮样式 */
.chat-sidebar__add-button {
  flex-shrink: 0;
}

/* 会话列表容器样式 */
.chat-sidebar__conversations {
  flex: 1 1 auto;
  overflow: auto;
  padding-bottom: 70px;
}

/* 加载中和空状态容器 */
.chat-sidebar__placeholder {
  text-align: center;
  padding: 20px 0;
}

/* 会话分组样式 */
.chat-sidebar__group {
  margin-bottom: 10px;
}

/* 会话日期标签样式 - 使用Ant Design风格 */
.chat-sidebar__date-label {
  padding: 8px 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin: 4px 0;
  text-align: left;
}

/* 会话项样式 - 使用Ant Design风格 */
.chat-sidebar__item {
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 4px;
  margin: 0 8px 4px 8px;
  background-color: transparent;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

/* 会话项内容样式 */
.chat-sidebar__item-content {
  width: 100%;
  overflow: hidden;
  flex: 1; /* 使内容占据所有可用空间 */
}

/* 会话项文本样式 - 使用Ant Design风格 */
.chat-sidebar__item-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5715;
}

/* 活动会话项文本样式 - 使用Ant Design风格 */
.chat-sidebar__item-text--active {
  font-size: 14px;
  color: #1677ff;
  font-weight: 400; /* 从500改为400，保持与普通文本一致的粗细 */
  line-height: 1.5715;
}

/* 删除按钮样式 */
.chat-sidebar__delete-button {
  opacity: 0.5;
  transition: opacity 0.2s ease;
  padding: 0 4px; /* 减小按钮的内边距 */
  margin-left: 4px; /* 减小与左侧内容的间距 */
}

/* 三点菜单按钮样式 */
.chat-sidebar__more-button {
  opacity: 0.5;
  transition: opacity 0.2s ease;
  padding: 0 4px; /* 减小按钮的内边距 */
  margin-left: 4px; /* 减小与左侧内容的间距 */
}

/* 覆盖 Ant Design List.Item 的默认操作区域样式 */
.chat-sidebar__item .ant-list-item-action {
  margin-left: 4px !important; /* 强制减小操作区域的左边距 */
  padding-left: 0;
}

/* 确保会话项在水平方向紧凑排列 */
.chat-sidebar__item.ant-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 8px; /* 使右边和删除按钮之间的空间更紧凑 */
}

.chat-sidebar__delete-button:hover {
  opacity: 1;
}

.chat-sidebar__more-button:hover {
  opacity: 1;
}

/* 鼠标悬停效果 - 使用Ant Design风格 */
.chat-sidebar__item:hover {
  background-color: #f5f5f5;
}

/* 活动会话项样式 - 使用Ant Design风格 */
.chat-sidebar__item--active {
  background-color: #e6f4ff;
  border-left: 3px solid #1677ff;
  padding-left: 13px;
}

/* 用户信息区域样式 */
.chat-sidebar__user-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #f9f9f9; /* 与侧边栏保持一致 */
  display: flex;
  align-items: center;
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
  box-sizing: border-box; /* 确保内边距不会增加元素宽度 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 用户头像样式 */
.chat-sidebar__avatar {
  margin-right: 12px;
}

/* 用户名称容器 */
.chat-sidebar__user-name {
  flex: 1;
}

/* 侧边栏下拉菜单 */
.chat-sidebar__dropdown-menu {
  margin-left: 16px;
}

/* 会话列表样式 */
.chat-sidebar__list {
  padding: 0;
  margin: 0;
}

/* 自定义滑动条样式 - 仅在滑动时显示 */
/* 水平内边距确保内容不被滑动条紧迫 */
.chat-sidebar__conversations {
  /* 将右侧内边距减小，使滑动条更紧凑 */
  padding-right: 2px;
  /* Firefox兼容性 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.08) transparent;
  /* 滑动时滑动条需要更清晰一些 */
}

/* 滑动条宽度 - 减小宽度使其更紧凑 */
.chat-sidebar__conversations::-webkit-scrollbar {
  width: 4px; /* 从6px减少到4px */
  /* 设置滑动条悬浮在内容上，不占用内容空间 */
  position: absolute;
  right: 0;
}

/* 设置滑块外观 */
.chat-sidebar__conversations::-webkit-scrollbar-thumb {
  /* 提高透明度使滑动时更易看见 */
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  /* 使用弱边框样式增强可见度 */
  border: 1px solid rgba(0, 0, 0, 0.03);
}

/* 设置滑道为透明 */
.chat-sidebar__conversations::-webkit-scrollbar-track {
  background-color: transparent;
  /* 确保滑道不占位置 */
  margin: 2px;
}

/* 默认情况下使滑块半透明 */
.chat-sidebar__conversations::-webkit-scrollbar-thumb {
  opacity: 0.3;
}

/* 滑动区域悬停时增强可见度 */
.chat-sidebar__conversations:hover::-webkit-scrollbar-thumb {
  opacity: 0.6;
  background-color: rgba(0, 0, 0, 0.12);
}

/* 当正在滑动时显示更明显的滑块 */
.chat-sidebar__conversations::-webkit-scrollbar-thumb:active {
  background-color: rgba(0, 0, 0, 0.15);
  opacity: 1;
}

/* 会话项样式 - 调整为更紧凑的布局 */
.chat-sidebar__item {
  /* 减少右侧内边距，使会话项与滑动条之间的间距更小 */
  padding: 12px 4px 12px 16px;
  /* 使文本占据更大空间 */
  box-sizing: border-box;
}

/* 设置按钮 */
.chat-sidebar__settings {
  margin-left: 8px;
  flex-shrink: 0; /* 防止按钮被压缩 */
}
