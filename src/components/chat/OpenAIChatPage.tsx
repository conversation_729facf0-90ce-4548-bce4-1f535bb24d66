import { useXAgent, useXChat } from '@ant-design/x';
import { App, message } from 'antd'; // 导入 App 组件
import OpenAI from 'openai';
import React, { useEffect, useReducer, useRef } from 'react';
import { OPENAI_CONFIG } from '../../config/api.config';
import { getAuthTicket } from '../../services/authService';
import {
  ChatInput,
  ChatSidebar,
  MessageList
} from './components';
import {
  useConversationActions,
  useConversations,
  useMessages,
  useSendMessage
} from './hooks';
import './OpenAIChatPage.css';
import { chatReducer, initialChatState } from './state';
import { getDateStrings } from './utils';

// 使用统一的API配置
const client = new OpenAI({
  baseURL: OPENAI_CONFIG.BASE_URL,
  apiKey: 'placeholder', // 使用占位符，实际的鉴权通过 headers 传递
  dangerouslyAllowBrowser: true, // 注意：生产环境中不建议这样做
  defaultHeaders: {
    "Accept": "text/event-stream",
    "Content-Type": "application/json"
  }
});

/**
 * OpenAI聊天页面组件
 * 基于@ant-design/x的useXAgent和useXChat实现与OpenAI API的集成
 * 使用自定义 Hook 抽取复杂逻辑，使组件更清晰可维护
 */
const OpenAIChatPage: React.FC = () => {
  // 获取日期字符串
  const { today: todayStr, yesterday: yesterdayStr } = getDateStrings();

  // 使用 message 的消息实例（钩子API，确保消息正常工作）
  const [messageApi, contextHolder] = message.useMessage();

  // 使用 useReducer 管理状态
  const [state, dispatch] = useReducer(chatReducer, initialChatState);

  // 使用 useRef 存储当前的 chatId，解决闭包问题
  const chatIdRef = useRef<string>('');

  // 退出标志，用于处理 cancel 事件
  const cancelFlag = useRef<boolean>(false);

  // 初始化 XAgent 配置 OpenAI 请求处理
  const [agent] = useXAgent({
    request: async (info, callbacks) => {
      const { message } = info;
      const { onSuccess, onUpdate, onError } = callbacks;

      // 使用 chatIdRef 获取最新的 chatId，避免闭包问题
      const chatId = chatIdRef.current || state.activeChatId;

      // 如果没有活动会话，发出警告
      if (!chatId) {
        console.warn('警告: 没有有效的会话 ID，但仍然尝试发送请求');
      }

      let content: string = '';

      try {
        // 确保消息内容不为undefined
        const userMessage = message || '';

        // 动态获取最新的鉴权ticket
        const currentTicket = getAuthTicket();
        if (!currentTicket) {
          throw new Error('未找到有效的鉴权信息，请重新登录');
        }

        const stream = await client.chat.completions.create(
          {
            model: 'gpt-3.5-turbo',
            // if chat context is needed, modify the array
            messages: [{ role: 'user', content: userMessage }],
            // stream mode
            stream: true,
          },
          {
            // 添加自定义请求头，包含 chatId 和动态鉴权信息
            headers: {
              'X-Chat-ID': chatId,
              'Authorization': currentTicket // 动态设置鉴权头
            }
          }
        );

        for await (const chunk of stream) {
          if(cancelFlag.current) {
            break;
          }
          content += chunk.choices[0]?.delta?.content || '';

          // 使用类型断言临时解决类型问题
          onUpdate(content as any);
        }


        cancelFlag.current = false;
        onSuccess(content as any);

        // 消息响应完成，重置发送按钮状态
        dispatch({ type: 'SET_SENDING_MESSAGE', payload: false });
      } catch (error) {
        console.error('发送消息失败:', error);

        // 根据错误类型显示不同的提示信息
        let errorMessage = '发送消息失败，请稍后重试';

        if (error instanceof Error) {
          if (error.message.includes('鉴权信息')) {
            errorMessage = '身份验证失效，请刷新页面重新登录';
          } else if (error.message.includes('网络')) {
            errorMessage = '网络连接异常，请检查网络后重试';
          } else if (error.message.includes('超时')) {
            errorMessage = '请求超时，请稍后重试';
          } else {
            errorMessage = `发送失败：${error.message}`;
          }
        }

        // 显示错误提示
        messageApi.error(errorMessage);

        // handle error
        onError(error as Error);
        // 发生错误时也需重置发送按钮状态
        dispatch({ type: 'SET_SENDING_MESSAGE', payload: false });
      }
    },
  });

  // 聚合参数
  const xchatConfig = {
    agent,
    defaultMessages: [],
    requestPlaceholder: '正在思考中...'
  };

  // 使用 XChat
  const { onRequest, messages: xMessages, setMessages } = useXChat(xchatConfig);

  // 使用自定义 Hook 处理消息和滚动
  const { formattedMessages, messagesContainerRef } = useMessages(xMessages);

  // 使用自定义 Hook 管理会话列表
  const { fetchConversations } = useConversations(dispatch, todayStr, yesterdayStr);

  // 使用自定义 Hook 管理会话操作
  const {
    handleConversationClick,
    handleAddConversation,
    handleDeleteConversation,
    createConversationIfNeeded
  } = useConversationActions(dispatch, messageApi, setMessages, todayStr, chatIdRef);

  // 使用自定义 Hook 处理发送消息
  const handleSendMessage = useSendMessage(dispatch, onRequest, messageApi, createConversationIfNeeded);

  // 处理输入框变化
  const handleInputChange = (value: string) => {
    dispatch({ type: 'SET_INPUT', payload: value });
  };

  // 处理取消
  const handleCancel = () => {
    cancelFlag.current = true;
    dispatch({ type: 'SET_SENDING_MESSAGE', payload: false });
    dispatch({ type: 'SET_INPUT', payload: '' });
  };

  // 首次加载时获取会话列表
  useEffect(() => {
    fetchConversations(setMessages);
  }, [fetchConversations, setMessages]);

  return (
    <App>  {/* 添加 App 组件包裹提供消息上下文 */}
      {contextHolder}  {/* 显示消息通知的占位符 */}
      <div className="chat-page-container">
        <ChatSidebar
          loading={state.loading}
          activeChatId={state.activeChatId}
          conversationItems={state.conversationsItems}
          onConversationClick={handleConversationClick}
          onAddConversation={handleAddConversation}
          onDeleteConversation={(key: string) => handleDeleteConversation(key, state.activeChatId)}
        />
        <div className="chat-content-wrapper">
          <div className="chat-content">
            <div className="messages-container" ref={messagesContainerRef}>
              <MessageList messages={formattedMessages} />
            </div>
            <ChatInput
              value={state.inputValue}
              loading={state.sendingMessage}
              onChange={handleInputChange}
              onSend={() => handleSendMessage(state.inputValue, state.activeChatId)}
              onCancel={() => handleCancel() }
            />
          </div>
        </div>
      </div>
    </App>
  );
};

export default OpenAIChatPage;
