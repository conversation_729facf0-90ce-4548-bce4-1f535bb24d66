.chat-page-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    background: #fff;
    font-family: AlibabaPuHuiTi, -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
  }
  
  .chat-menu {
    background: #ffffff;
    width: 280px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
  }
  
  .chat-content-wrapper {
    flex: 1;
    display: flex;
    justify-content: center;
    background-color: #ffffff;
    overflow: auto;
    width: calc(100% - 280px); /* 减去侧边栏宽度 */
    margin-left: 280px; /* 与侧边栏宽度一致 */
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
  }
  
  .chat-logo {
    display: flex;
    height: 72px;
    align-items: center;
    justify-content: start;
    padding: 0 24px;
    box-sizing: border-box;
  }
  
  .logo-img {
    width: 24px;
    height: 24px;
    display: inline-block;
  }
  
  .logo-text {
    display: inline-block;
    margin: 0 8px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.88);
    font-size: 16px;
  }
  
  .add-conversation-btn {
    background: rgba(22, 119, 255, 0.06);
    border: 1px solid rgba(22, 119, 255, 0.2);
    width: calc(100% - 24px);
    margin: 0 12px 24px 12px;
  }
  
  .conversations-container {
    overflow-y: auto;
    flex: 1;
    height: calc(100% - 120px);
    padding-bottom: 70px; /* 为底部用户信息区域留出空间 */
  }
  
  .conversation-group {
    margin-bottom: 16px;
  }
  
  .conversation-date-label {
    padding: 8px 0 4px 12px;
    font-weight: bold;
    color: #888;
    font-size: 15px;
  }
  
  .chat-content {
    height: 100%;
    width: 100%;
    max-width: 800px;
    flex: none;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 24px;
    gap: 16px;
    overflow: hidden;
    position: relative;
  }
  
  /* 确保输入框吸底 */
  .chat-content .ant-sender {
    position: absolute;
    bottom: 24px;
    left: 24px;
    right: 24px;
    background-color: #fff;
    z-index: 10;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    width: calc(100% - 48px); /* 确保宽度正确，左右24px + 右24px） */
    max-width: 752px; /* 限制最大宽度，与内容区域保持一致（chat-content的max-width - 两侧内边距） */
  }
  
  /* 修复文本输入框的样式 */
  .chat-content .ant-sender .ant-sender-input {
    width: 100%;
    padding-right: 56px; /* 为右侧按钮留出足够空间 */
    box-sizing: border-box;
  }
  
  /* 修正输入框内部组件布局 */
  .chat-content .ant-sender .ant-sender-content {
    width: 100%;
    position: relative;
  }

  /* 确保发送按钮正确定位 */
  .chat-content .ant-sender .ant-sender-actions-list {
    position: absolute;
    right: 12px;
    bottom: 6px;
  }
  
  /* 添加确保输入框在宽度较小的情况下也能正常显示 */
  @media screen and (max-width: 600px) {
    .chat-content .ant-sender {
      width: calc(100% - 32px);
      left: 16px;
      right: 16px;
    }
  }
  
  .messages-container {
    flex: 1;
    overflow: auto;
    padding: 10px;
    padding-bottom: 80px; /* 为底部输入框留出空间 */
    margin-bottom: 24px; /* 增加底部间距 */
  }
  
  .placeholder-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 120px; /* 为底部输入框留出空间 */
    padding-top: 32px;
  }
  
  .sender-container {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  

/* 消息列表滚动条样式 - 与侧边栏保持一致 */
/* 为消息列表容器添加滚动条样式 */
.messages-container {
  /* Firefox兼容性 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.08) transparent;
}

/* 滚动条宽度 - 精简设计 */
.messages-container::-webkit-scrollbar {
  width: 4px;
  /* 设置滚动条悬浮在内容上，不占用内容空间 */
  position: absolute;
  right: 0;
}

/* 滚动块外观设置 */
.messages-container::-webkit-scrollbar-thumb {
  /* 半透明的灰色调 */
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  /* 使用弱边框增强可见度 */
  border: 1px solid rgba(0, 0, 0, 0.03);
}

/* 设置滚动轨道为透明 */
.messages-container::-webkit-scrollbar-track {
  background-color: transparent;
  /* 确保轨道不占位置 */
  margin: 2px;
}

/* 默认情况下滚动块半透明 */
.messages-container::-webkit-scrollbar-thumb {
  opacity: 0.3;
}

/* 滚动区域悬停时增强可见度 */
.messages-container:hover::-webkit-scrollbar-thumb {
  opacity: 0.6;
  background-color: rgba(0, 0, 0, 0.12);
}

/* 滚动时显示更明显的滚动块 */
.messages-container::-webkit-scrollbar-thumb:active {
  background-color: rgba(0, 0, 0, 0.15);
  opacity: 1;
}